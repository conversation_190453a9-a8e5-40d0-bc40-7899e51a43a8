# Tracing com OpenTelemetry

**Tracing** (rastreamento distribuído) é a prática de monitorar e rastrear o caminho de requisições enquanto elas percorrem diferentes partes de um sistema, permitindo identificar gargalos, falhas e entender o desempenho ponta-a-ponta.

**OpenTelemetry** é um framework open-source que padroniza a coleta de métricas, logs e traces em aplicações, facilitando a instrumentação automática e manual de sistemas em diversas linguagens.

**Jaeger** é uma ferramenta de visualização e análise de traces distribuídos, permitindo inspecionar o fluxo das requisições, identificar problemas de performance e analisar dependências entre serviços.

### Fluxo Visual

```mermaid
flowchart TD
    A["Requisição HTTP"] --> B["Fastify"]
    B --> C["OpenTelemetry Instrumentation"]
    C --> D["Exporter (<PERSON>ae<PERSON>/<PERSON><PERSON>kin)"]
    D --> E["Visualização dos Traces"]
```

## Guia rápido: Como usar o tracing no projeto

### 1. Subir o ambiente com Jaeger (O nosso docker compose já sobe o container do jaeger automaticamente)

```bash
docker compose up -d 
```

- Isso sobe o todos os container e o Jaeger (interface web em http://localhost:16686), a API já configurada para enviar traces.

### 2. Fazer requisições para a API

Exemplo:
```bash
curl http://localhost:3000/health
```
Ou acesse qualquer rota da API normalmente.

### 3. Visualizar traces no Jaeger
- Acesse [http://localhost:16686](http://localhost:16686)
- No campo "Service", selecione `fastify-api-petrus-boilerplate` (ou o nome definido no tracing)
- Clique em "Find Traces"
- Clique em um trace para ver detalhes (tempo, spans, atributos, erros, etc)

### 4. Como funciona a configuração
- O tracing é inicializado automaticamente ao subir a API.
- O endpoint do Jaeger é configurado via variável de ambiente `OTEL_EXPORTER_OTLP_ENDPOINT` (já setada no docker-compose).
- O nome do serviço pode ser alterado em `src/shared/config/tracing.ts`.

### 5. Dicas
- Para rodar localmente (fora do Docker), use o endpoint `http://localhost:4318/v1/traces`.
- Para rodar em container, use `http://jaeger:4318/v1/traces` (já configurado).
- O tracing automático cobre HTTP, banco, etc. Para rastrear operações específicas, adicione spans manuais (ver exemplos no README ou peça para o time de backend).

---