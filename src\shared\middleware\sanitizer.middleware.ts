import type { FastifyRequest, FastifyReply, FastifyInstance } from 'fastify';
import { sanitizeResponse } from '../utils/data-manipulation';

/**
 * Helper function to create a sanitizer hook for specific routes
 * Usage: fastify.addHook('onSend', createSanitizerHook());
 */
export const createSanitizerHook = () => {
  return async (
    request: FastifyRequest,
    reply: FastifyReply,
    payload: any
  ) => {
    console.log('🧹 SANITIZER CALLED:', typeof payload, payload);
    
    // Always sanitize objects, regardless of content-type
    if (payload && typeof payload === 'object') {
      const sanitized = sanitizeResponse(payload);
      console.log('🧹 SANITIZED:', sanitized);
      return sanitized;
    }
    
    // Handle string payloads that might be JSON
    if (typeof payload === 'string') {
      try {
        const parsed = JSON.parse(payload);
        const sanitized = sanitizeResponse(parsed);
        console.log('🧹 SANITIZED STRING:', sanitized);
        return JSON.stringify(sanitized);
      } catch {
        // If parsing fails, return original payload
        return payload;
      }
    }
    
    return payload;
  };
};

/**
 * Plugin to register sanitizer for a specific module
 * Usage in routes: await fastify.register(sanitizerPlugin);
 */
export const sanitizerPlugin = async (fastify: FastifyInstance) => {
  console.log('🔧 REGISTERING SANITIZER PLUGIN');
  
  // Use onSend hook to sanitize responses
  fastify.addHook('onSend', createSanitizerHook());
  
  // Add serializer for JSON responses
  fastify.addContentTypeParser('application/json', { parseAs: 'string' }, (req, body, done) => {
    try {
      const json = JSON.parse(body as string);
      done(null, json);
    } catch (err: any) {
      done(err, undefined);
    }
  });
  
  // Add a serializer that will sanitize all JSON responses
  fastify.setSerializerCompiler(({ schema, method, url, httpStatus }) => {
    return (data: any) => {
      console.log('📝 SERIALIZER CALLED:', { method, url, httpStatus, data });
      if (data && typeof data === 'object') {
        const sanitized = sanitizeResponse(data);
        console.log('📝 SERIALIZER SANITIZED:', sanitized);
        return JSON.stringify(sanitized);
      }
      return JSON.stringify(data);
    };
  });
}; 