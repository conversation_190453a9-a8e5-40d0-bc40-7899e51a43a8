# ==============================================
# APPLICATION CONFIGURATION
# ==============================================
NODE_ENV=development
PORT=3000

# ==============================================
# DATABASE CONFIGURATION
# ==============================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=fastwhite
DB_USER=postgres
DB_PASSWORD=postgres123

# ==============================================
# AWS SQS CONFIGURATION
# ==============================================
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1

# ==============================================
# AUTHENTICATION & SECURITY
# ==============================================
JWT_SECRET=your-super-secret-jwt-key-change-in-production-minimum-32-characters
JWT_EXPIRES_IN=1h // {number}{s | m | h | d | w | y}
JWT_REFRESH_EXPIRES_IN=7d // {number}{s | m | h | d | w | y}
COOKIE_SECRET=your-super-secret-cookie-key-change-in-production-minimum-32-charactersms
BCRYPT_ROUNDS=10

# ==============================================
# DEVELOPMENT CONFIGURATION
# ==============================================
# Set to true to enable detailed logging
DEBUG=false

# Set to true to enable CORS for all origins (development only)
CORS_ENABLED=true

# API Rate limiting (requests per minute)
RATE_LIMIT=100

# ==============================================
# BullMQ CONFIGURATION
# ==============================================
# Redis (BullMQ)
REDIS_HOST=localhost
REDIS_PORT=6379
# REDIS_PASSWORD= # (opcional, se seu Redis exigir senha)

# Resend (envio de email)
RESEND_API_KEY=your_resend_api_key

# NASA APOD API
NASA_APOD_API_KEY=DEMO_KEY