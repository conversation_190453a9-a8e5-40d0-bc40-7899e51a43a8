import { db } from '@/shared/database/connection';
import { users } from '../../entites/user.entity';
import { eq, or, like } from 'drizzle-orm';

/**
 * ESTRATÉGIA 1: Limpeza por IDs específicos (Recomendada)
 * - Armazena IDs criados durante o teste
 * - Remove apenas os dados criados pelo teste
 */
export class TestDataCleanup {
  private createdUserIds: number[] = [];

  addCreatedUserId(id: number) {
    this.createdUserIds.push(id);
  }

  async cleanup() {
    if (this.createdUserIds.length > 0) {
      try {
        for (const id of this.createdUserIds) {
          await db.delete(users).where(eq(users.id, id));
        }
        console.log(
          `✅ Limpeza: ${this.createdUserIds.length} usuários removidos`
        );
        this.createdUserIds = [];
      } catch (error) {
        console.warn('⚠️ Erro ao limpar dados de teste:', error);
      }
    }
  }
}

/**
 * ESTRATÉGIA 2: Limpeza por padrão de teste
 * - Remove dados que seguem um padrão específico de teste
 */
export const cleanupTestUsers = async () => {
  try {
    await db
      .delete(users)
      .where(
        or(
          like(users.email, '%test%'),
          like(users.name, 'Test%'),
          like(users.username, 'test%')
        )
      );
    console.log('✅ Usuários de teste removidos');
  } catch (error) {
    console.warn('⚠️ Erro ao limpar usuários de teste:', error);
  }
};

/**
 * ESTRATÉGIA 3: Factory para criar dados de teste
 * - Cria dados padronizados e facilita limpeza
 */
export const createTestUser = async (overrides: Partial<any> = {}) => {
  const defaultUser = {
    name: 'Test User',
    email: `test${Date.now()}@example.com`,
    username: `testuser${Date.now()}`,
    password: 'password123',
    ...overrides,
  };

  return defaultUser;
};

/**
 * Exemplo de como usar as estratégias:
 *
 * describe('User Tests', () => {
 *   let cleanup: TestDataCleanup;
 *
 *   beforeEach(() => {
 *     cleanup = new TestDataCleanup();
 *   });
 *
 *   afterEach(async () => {
 *     await cleanup.cleanup();
 *   });
 *
 *   it('should create user', async () => {
 *     const userData = await createTestUser();
 *     const user = await userService.create(userData);
 *     cleanup.addCreatedUserId(user.id);
 *
 *
 *   });
 * });
 */
