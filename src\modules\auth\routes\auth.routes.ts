import type { FastifyInstance } from 'fastify';
import * as authController from '../controllers/auth.controllers';
import fastifyCookie from '@fastify/cookie';

export const registerAuthRoutes = async (fastify: FastifyInstance) => {
  fastify.post('/login', {
    handler: authController.login,
  });

  fastify.post('/refresh-token', {
    handler: authController.refreshToken,
  });

  fastify.post('/logout', {
    handler: authController.logout,
  });
};
