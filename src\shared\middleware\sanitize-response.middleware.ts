import type { FastifyRequest, FastifyReply } from 'fastify';

const SENSITIVE_FIELDS = [
  'password',
  'senha',
  'token',
  'secret',
  'key',
] as const;

type SensitiveField = (typeof SENSITIVE_FIELDS)[number];

type ContainsSensitiveContent<K extends string> =
  K extends `${string}${SensitiveField}${string}`
    ? true
    : K extends `${SensitiveField}${string}`
    ? true
    : K extends `${string}${SensitiveField}`
    ? true
    : false;

type SanitizedObject<T> = T extends readonly (infer U)[]
  ? SanitizedObject<U>[]
  : T extends object
  ? {
      [K in keyof T as ContainsSensitiveContent<string & K> extends true
        ? never
        : K]: SanitizedObject<T[K]>;
    }
  : T;

export const sanitizeResponse = (keys: string[] = ['password', 'amount']) => {
  const sanitizeData = <T>(data: T): SanitizedObject<T> => {
    if (data === null || data === undefined) {
      return data as SanitizedObject<T>;
    }

    if (Array.isArray(data)) {
      return data.map((item) => sanitizeData(item)) as SanitizedObject<T>;
    }

    if (typeof data === 'object') {
      const sanitized = {} as Record<string, any>;

      for (const [key, value] of Object.entries(data)) {
        // Check if key is exactly a sensitive field name or contains it
        const isSensitive = SENSITIVE_FIELDS.some(
          (field) =>
            key.toLowerCase() === field.toLowerCase() ||
            key.toLowerCase().includes(field.toLowerCase())
        );

        if (isSensitive) {
          continue;
        }

        sanitized[key] = sanitizeData(value);
      }

      return sanitized as SanitizedObject<T>;
    }

    return data as SanitizedObject<T>;
  };
  return async (request: FastifyRequest, reply: FastifyReply, payload: any) => {
    const contentType = reply.getHeader('content-type');

    if (contentType && contentType.toString().includes('application/json')) {
      const jsonPayload = JSON.parse(payload.toString());
      const sanitized = sanitizeData(jsonPayload);
      return JSON.stringify(sanitized);
    }
  };
};
