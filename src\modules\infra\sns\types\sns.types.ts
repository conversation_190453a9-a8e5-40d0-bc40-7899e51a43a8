import type { MessageAttributeValue } from '@aws-sdk/client-sns';

export interface PublishMessageParams {
  topicName: string;
  message: string;
  subject?: string;
  messageAttributes?: Record<string, MessageAttributeValue>;
}

export interface PublishJsonMessageParams {
  topicName: string;
  data: Record<string, unknown>;
  subject?: string;
  messageAttributes?: Record<string, MessageAttributeValue>;
}

export interface PublishBatchParams {
  topicName: string;
  messages: Array<{
    id: string;
    message: string;
    subject?: string;
    messageAttributes?: Record<string, MessageAttributeValue>;
  }>;
}

export interface SubscribeParams {
  topicName: string;
  protocol:
    | 'email'
    | 'sms'
    | 'sqs'
    | 'http'
    | 'https'
    | 'application'
    | 'lambda';
  endpoint: string;
  attributes?: Record<string, string>;
}

export interface CreateTopicParams {
  name: string;
  displayName?: string;
  policy?: string;
  deliveryPolicy?: string;
  attributes?: Record<string, string>;
}

export interface SnsNotification {
  Type: string;
  MessageId: string;
  TopicArn: string;
  Subject?: string;
  Message: string;
  Timestamp: string;
  SignatureVersion: string;
  Signature: string;
  SigningCertURL: string;
  UnsubscribeURL: string;
  MessageAttributes?: Record<
    string,
    {
      Type: string;
      Value: string;
    }
  >;
}

export interface SnsSubscriptionConfirmation {
  Type: 'SubscriptionConfirmation';
  MessageId: string;
  Token: string;
  TopicArn: string;
  Message: string;
  SubscribeURL: string;
  Timestamp: string;
  SignatureVersion: string;
  Signature: string;
  SigningCertURL: string;
}

export interface SnsMessage {
  id: string;
  message: string;
  subject?: string;
  messageAttributes?: Record<string, MessageAttributeValue>;
}
