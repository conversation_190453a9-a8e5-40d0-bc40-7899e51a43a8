import {
  PublishCommand,
  Publish<PERSON>atchCommand,
  CreateTopicCommand,
  ListTopicsCommand,
  SubscribeCommand,
  DeleteTopicCommand,
  UnsubscribeCommand,
  ListSubscriptionsByTopicCommand,
} from '@aws-sdk/client-sns';
import { createSnsClient } from './client/sns-client';
import { MissingResourceException } from '../../../shared/errors/custom-exceptions';

const snsClient = createSnsClient();

/**
 * Busca o ARN do tópico SNS a partir do nome do tópico.
 * Lança MissingResourceException se o tópico não for encontrado.
 * @param topicName Nome do tópico SNS
 * @returns ARN do tópico SNS
 */
async function getTopicArn(topicName: string): Promise<string> {
  const command = new ListTopicsCommand({});
  const response = await snsClient.send(command);

  const topic = response.Topics?.find((t) =>
    t.TopicArn?.endsWith(`:${topicName}`)
  );
  if (!topic?.TopicArn) {
    throw new MissingResourceException(
      `Topic ARN not found for topic name: ${topicName}`
    );
  }
  return topic.TopicArn;
}

/**
 * Publica uma mensagem para o tópico SNS especificado.
 * @param topicName Nome do tópico SNS
 * @param message Mensagem a ser enviada
 * @param options Opções adicionais do SNS (opcional)
 * @returns Resposta do AWS SNS
 */
export async function publishMessage(
  topicName: string,
  message: string,
  options: Record<string, any> = {}
) {
  const topicArn = await getTopicArn(topicName);
  const command = new PublishCommand({
    TopicArn: topicArn,
    Message: message,
    ...options,
  });
  return snsClient.send(command);
}

/**
 * Publica uma mensagem JSON estruturada para o tópico SNS especificado.
 * @param topicName Nome do tópico SNS
 * @param data Dados a serem enviados como JSON
 * @param options Opções adicionais do SNS (opcional)
 * @returns Resposta do AWS SNS
 */
export async function publishJsonMessage(
  topicName: string,
  data: Record<string, any>,
  options: Record<string, any> = {}
) {
  return publishMessage(topicName, JSON.stringify(data), options);
}

/**
 * Publica múltiplas mensagens em lote para o tópico SNS especificado.
 * @param topicName Nome do tópico SNS
 * @param messages Array de mensagens com id e message
 * @param options Opções adicionais do SNS (opcional)
 * @returns Resposta do AWS SNS
 */
export async function publishBatch(
  topicName: string,
  messages: Array<{ id: string; message: string; [key: string]: any }>,
  options: Record<string, any> = {}
) {
  const topicArn = await getTopicArn(topicName);
  const entries = messages.map((msg) => ({
    Id: msg.id,
    Message: msg.message,
    ...Object.fromEntries(
      Object.entries(msg).filter(([key]) => key !== 'id' && key !== 'message')
    ),
  }));

  const command = new PublishBatchCommand({
    TopicArn: topicArn,
    PublishBatchRequestEntries: entries,
    ...options,
  });
  return snsClient.send(command);
}

/**
 * Cria um novo tópico SNS.
 * @param topicName Nome do tópico SNS
 * @param options Opções adicionais do SNS (opcional)
 * @returns Resposta do AWS SNS
 */
export async function createTopic(
  topicName: string,
  options: Record<string, any> = {}
) {
  const command = new CreateTopicCommand({
    Name: topicName,
    ...options,
  });
  return snsClient.send(command);
}

/**
 * Subscreve um endpoint ao tópico SNS especificado.
 * @param topicName Nome do tópico SNS
 * @param protocol Protocolo da subscrição (email, sms, sqs, http, https, application, lambda)
 * @param endpoint Endpoint da subscrição
 * @param options Opções adicionais do SNS (opcional)
 * @returns Resposta do AWS SNS
 */
export async function subscribe(
  topicName: string,
  protocol: string,
  endpoint: string,
  options: Record<string, any> = {}
) {
  const topicArn = await getTopicArn(topicName);
  const command = new SubscribeCommand({
    TopicArn: topicArn,
    Protocol: protocol,
    Endpoint: endpoint,
    ...options,
  });
  return snsClient.send(command);
}

/**
 * Remove uma subscrição do SNS usando o ARN da subscrição.
 * @param subscriptionArn ARN da subscrição a ser removida
 * @returns Resposta do AWS SNS
 */
export async function unsubscribe(subscriptionArn: string) {
  const command = new UnsubscribeCommand({
    SubscriptionArn: subscriptionArn,
  });
  return snsClient.send(command);
}

/**
 * Lista todas as subscrições de um tópico SNS especificado.
 * @param topicName Nome do tópico SNS
 * @param options Opções adicionais do SNS (opcional)
 * @returns Resposta do AWS SNS contendo as subscrições
 */
export async function listSubscriptions(
  topicName: string,
  options: Record<string, any> = {}
) {
  const topicArn = await getTopicArn(topicName);
  const command = new ListSubscriptionsByTopicCommand({
    TopicArn: topicArn,
    ...options,
  });
  return snsClient.send(command);
}

/**
 * Lista todos os tópicos SNS.
 * @param options Opções adicionais do SNS (opcional)
 * @returns Resposta do AWS SNS contendo os tópicos
 */
export async function listTopics(options: Record<string, any> = {}) {
  const command = new ListTopicsCommand(options);
  return snsClient.send(command);
}

/**
 * Deleta um tópico SNS especificado.
 * @param topicName Nome do tópico SNS
 * @returns Resposta do AWS SNS
 */
export async function deleteTopic(topicName: string) {
  const topicArn = await getTopicArn(topicName);
  const command = new DeleteTopicCommand({
    TopicArn: topicArn,
  });
  return snsClient.send(command);
}
