import bcrypt from 'bcryptjs';
import * as userRepository from '../repositories/user.repository';
import { NotFoundException } from '../../../shared/errors/custom-exceptions';
import type {
  User,
  CreateUserInput,
  UpdateUserInput,
  UserFilters,
  PaginationOptions,
} from '../types/user.types';
import { getCache, setCache, invalidateCache } from '../../../shared/cache';

export const create = async (userData: CreateUserInput) => {
  const hashedPassword = await bcrypt.hash(userData.password, 12);

  const user = await userRepository.create({
    ...userData,
    password: hashedPassword,
    role: 'user',
    isActive: true,
  });

  // Invalida o cache após a criação
  await invalidateCache('users:default:page=1:limit=10');
  return user;
};

export const getById = async (id: number) => {
  const cacheKey = `user:${id}`;
  const cachedUser = await getCache<User>(cacheKey);
  if (cachedUser) {
    return cachedUser;
  }
  const user = await userRepository.getById(id);
  if (!user) {
    throw new NotFoundException('User not found');
  }
  await setCache(cacheKey, user, 600); // 10 minutos
  return user;
};

export const getAll = async (
  filters: UserFilters,
  pagination: PaginationOptions
) => {
  const isDefaultQuery =
    (!filters || Object.keys(filters).length === 0) &&
    pagination && pagination.page === 1 && pagination.limit === 10;

  const cacheKey = 'users:default:page=1:limit=10';

  if (isDefaultQuery) {
    const cached = await getCache<typeof result>(cacheKey);
    if (cached) {
      return cached;
    }
  }

  const result = await userRepository.getAll(filters, pagination);

  if (isDefaultQuery) {
    await setCache(cacheKey, result, 120); // 2 minutos
  }

  return {
    ...result,
    data: result,
  };
};

export const update = async (id: number, userData: UpdateUserInput) => {
  const existingUser = await userRepository.getById(id);
  if (!existingUser) {
    throw new NotFoundException('User not found');
  }

  const updatedUser = await userRepository.update(id, userData);
  if (!updatedUser) {
    throw new NotFoundException('User not found');
  }

  // Invalida o cache após a atualização
  await invalidateCache('users:default:page=1:limit=10');
  await invalidateCache(`user:${id}`);
  return updatedUser;
};

export const softDelete = async (id: number): Promise<void> => {
  const user = await userRepository.getById(id);
  if (!user) {
    throw new NotFoundException('User not found');
  }

  await userRepository.softDelete(id);
  // Invalida o cache após a exclusão
  await invalidateCache('users:default:page=1:limit=10');
  await invalidateCache(`user:${id}`);
};
