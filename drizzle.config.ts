import { defineConfig } from 'drizzle-kit';
import { env } from './src/shared/config/env';

export default defineConfig({
  schema: './src/modules/*/entites/*.entity.ts',
  out: './drizzle',
  dialect: 'postgresql',
  dbCredentials: {
    host: env.DB_HOST,
    port: env.DB_PORT,
    user: env.DB_USER,
    password: env.DB_PASSWORD,
    database: env.DB_NAME,
    ssl: env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  },
});
