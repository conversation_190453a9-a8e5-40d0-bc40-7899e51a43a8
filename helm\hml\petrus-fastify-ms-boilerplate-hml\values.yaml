name: petrus-fastify-ms-boilerplate-hml
# replicaCount: 1
image:
  repository: 038462752057.dkr.ecr.us-east-2.amazonaws.com/fastify-ms-boilerplate
  tag: latest
  pullPolicy: Always
  containerPort: 3000
environment:
  NODE_ENV: "development"
  PORT: 3000
  DB_HOST: "fastfy-ms-boilerplate-database.cp2cmo04qs40.us-east-2.rds.amazonaws.com"
  DB_USER: "fastfymsboilerplate_user"
  DB_PORT: 5432
  DB_NAME: "fastfymsboilerplatedb"
  DB_SSL: "true"
  JWT_SECRET: "fastfy-ms-boilerplate-jwt-secret"
  JWT_EXPIRATION: "1h"
  JWT_REFRESH_EXPIRES_IN: "7d"
  COOKIE_SECRET: "fastfy-ms-boilerplate-cookie-secret"
  BCRYPT_ROUNDS: 10
  DEBUG: false
  CORS_ENABLED: true
  RATE_LIMIT: 100
  REDIS_HOST: "fastfy-ms-boilerplate.ddm2xt.ng.0001.use2.cache.amazonaws.com"
  REDIS_PORT: 6379
  RESEND_API_KEY: "fastfy-ms-boilerplate-resend-api-key"
  NASA_APOD_API_KEY: "fastfy-ms-boilerplate-nasa-apod-api-key"
  OTEL_EXPORTER_OTLP_ENDPOINT: http://jaeger:4318/v1/traces
resources:
  requests:
    cpu: 0.5
    memory: 200Mi
  limits:
    cpu: 0.5
    memory: 200Mi
livenessProbe:
  httpGet:
      path: {}
  initialDelaySeconds: 10
  failureThreshold: 3
  periodSeconds: 10
readinessProbe:
  tcpSocket: null
  initialDelaySeconds: 10
  periodSeconds: 10
  failureThreshold: 3
service:
  type: ClusterIP
  targetPort: 3000
ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
  hosts:
    - name: fastify-ms-boilerplate.hml.petrus-software.com
      path: /

######################################################################
env:
  secret:
    DB_PASSWORD: hml/fastfy-ms-boilerplate/DATABASE_URL
secretStoreRef:
  name: aws-auth-css
  kind: ClusterSecretStore

hpa:
  enabled: false
  # minReplicas: {}
  # maxReplicas: {}
  # averageCpuUtilization: {}
  # averageMemUtilization: {}
# nodeSelector:
#   project: #ex: petrus
