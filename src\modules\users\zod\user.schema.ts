import { z } from 'zod';

// Create user schema
export const createUserSchema = z.object({
  email: z.string().email(),
  username: z.string().min(3).max(30),
  password: z.string().min(8),
  name: z.string().min(1).max(100),
});

// Update user schema
export const updateUserSchema = z.object({
  email: z.string().email().optional(),
  username: z.string().min(3).max(30).optional(),
  name: z.string().min(1).max(100).optional(),
  isActive: z.boolean().optional(),
});

// Get users query schema
export const getUsersQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  search: z.string().optional(),
  role: z.enum(['admin', 'user']).optional(),
  isActive: z.coerce.boolean().optional(),
});

export const userIdSchema = z.object({
  id: z.coerce.number().positive(),
});

