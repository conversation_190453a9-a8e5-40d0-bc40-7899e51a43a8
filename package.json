{"name": "server", "version": "1.0.0", "description": "API Server with Package by Feature architecture", "main": "src/index.ts", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsup src/index.ts --format cjs,esm --dts", "start": "node dist/index.js", "db:generate": "drizzle-kit generate:pg", "db:migrate": "tsx src/shared/database/migrate.ts", "db:seed": "tsx src/shared/database/seeder.ts", "db:setup": "tsx src/shared/database/setup.ts", "db:clear": "tsx -e \"import { clearTestData } from './src/shared/database/seeder'; clearTestData();\"", "db:studio": "drizzle-kit studio", "test": "jest", "test:load": "tsx src/shared/load-test/load-cannon.ts", "worker:nasa": "tsx src/modules/nasa/workers/sendApodEmail.worker.ts"}, "dependencies": {"@aws-sdk/client-sns": "^3.846.0", "@aws-sdk/client-sqs": "^3.500.0", "@fastify/cookie": "^9.4.0", "@fastify/cors": "^8.4.2", "@fastify/rate-limit": "^8.1.0", "@fastify/swagger": "^8.14.0", "@fastify/swagger-ui": "^2.1.0", "@libsql/client": "^0.4.2", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.61.0", "@opentelemetry/exporter-trace-otlp-http": "^0.202.0", "@opentelemetry/sdk-node": "^0.202.0", "@types/pg": "^8.15.4", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "bullmq": "^5.56.1", "dotenv": "^16.6.1", "drizzle-kit": "^0.20.9", "drizzle-orm": "^0.29.3", "fastify": "^4.25.2", "ioredis": "^5.6.1", "isomorphic-dompurify": "^2.26.0", "jsonwebtoken": "^9.0.2", "pg": "^8.16.2", "pino-pretty": "^10.3.1", "resend": "^4.6.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.22.4"}, "devDependencies": {"@types/autocannon": "^7.12.7", "@types/bcryptjs": "^2.4.6", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.6", "@types/supertest": "^6.0.3", "autocannon": "^8.0.0", "autocannon-reporter": "^1.1.0", "jest": "^30.0.4", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "tsup": "^8.0.1", "tsx": "^4.7.0", "typescript": "^5.3.3"}}