# <PERSON>ó<PERSON>lo SNS (Simple Notification Service)

Este módulo fornece uma interface simples para interagir com o Amazon SNS (Simple Notification Service), seguindo a mesma estrutura do módulo SQS existente.

## Estrutura do Módulo

```
src/modules/infra/sns/
├── client/
│   └── sns-client.ts          # Cliente SNS configurado
├── examples/
│   └── sns-usage.example.ts   # Exemplos de uso
├── types/
│   └── sns.types.ts           # Tipos TypeScript
├── utils/
│   └── sns.utils.ts           # Utilitários SNS
├── sns.service.ts             # Serviço principal
└── README.md                  # Esta documentação
```

## Configuração

### Variáveis de Ambiente

Configure as seguintes variáveis de ambiente:

```env
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
```

## Uso Básico

### Importar o Serviço

```typescript
import * as snsService from '@/modules/infra/sns/sns.service';
```

### Principais Funcionalidades

#### 1. Criar um Tópico

```typescript
const result = await snsService.createTopic('user-notifications', {
  DisplayName: 'User Notifications Topic',
});
```

#### 2. Publicar Mensagem Simples

```typescript
await snsService.publishMessage(
  'user-notifications',
  'Hello World!',
  { Subject: 'Test Message' }
);
```

#### 3. Publicar Mensagem JSON

```typescript
await snsService.publishJsonMessage('user-notifications', {
  userId: 123,
  action: 'user_created',
  timestamp: new Date().toISOString(),
});
```

#### 4. Subscrever Endpoint

```typescript
// Email
await snsService.subscribe(
  'user-notifications',
  'email',
  '<EMAIL>'
);

// SMS
await snsService.subscribe(
  'user-notifications',
  'sms',
  '+5511999999999'
);

// SQS
await snsService.subscribe(
  'user-notifications',
  'sqs',
  'arn:aws:sqs:us-east-1:123456789012:my-queue'
);
```

#### 5. Publicar em Lote

```typescript
await snsService.publishBatch('user-notifications', [
  { id: '1', message: 'Message 1', Subject: 'Subject 1' },
  { id: '2', message: 'Message 2', Subject: 'Subject 2' },
]);
```

#### 6. Listar Tópicos e Subscrições

```typescript
// Listar tópicos
const topics = await snsService.listTopics();

// Listar subscrições de um tópico
const subscriptions = await snsService.listSubscriptions('user-notifications');
```

## Casos de Uso

### 1. Notificações de Usuário

```typescript
import * as snsService from '@/modules/infra/sns/sns.service';

export async function notifyUserCreated(user: User) {
  await snsService.publishJsonMessage('user-events', {
    eventType: 'USER_CREATED',
    userId: user.id,
    userEmail: user.email,
    timestamp: new Date().toISOString(),
  }, {
    Subject: 'New User Created',
  });
}
```

### 2. Alertas do Sistema

```typescript
export async function sendSystemAlert(level: 'info' | 'warning' | 'error', message: string) {
  await snsService.publishMessage('system-alerts', message, {
    Subject: `System Alert - ${level.toUpperCase()}`,
    MessageAttributes: {
      level: {
        DataType: 'String',
        StringValue: level,
      },
    },
  });
}
```

### 3. Comunicação entre Microserviços

```typescript
export async function publishServiceEvent(serviceName: string, eventData: any) {
  await snsService.publishJsonMessage('microservices-events', {
    source: serviceName,
    timestamp: new Date().toISOString(),
    data: eventData,
  }, {
    Subject: `Event from ${serviceName}`,
  });
}
```

## Utilitários

O módulo inclui utilitários para:

- Validação de notificações SNS
- Criação de atributos de mensagem
- Formatação de mensagens estruturadas
- Extração de informações de ARNs

```typescript
import * as snsUtils from '@/modules/infra/sns/utils/sns.utils';

// Validar notificação
if (snsUtils.isValidSnsNotification(payload)) {
  // Processar notificação
}

// Criar atributos de mensagem
const attributes = snsUtils.createMessageAttributes({
  priority: 'high',
  source: 'user-service',
});
```

## Tipos TypeScript

O módulo fornece tipos completos para todas as operações:

```typescript
import type { 
  SnsNotification, 
  SnsSubscriptionConfirmation,
  PublishMessageParams 
} from '@/modules/infra/sns/types/sns.types';
```

## Tratamento de Erros

O módulo utiliza as mesmas exceções customizadas do projeto:

- `InvalidCredentialsException`: Credenciais AWS inválidas
- `MissingResourceException`: Tópico não encontrado

## Exemplos Completos

Consulte o arquivo `examples/sns-usage.example.ts` para exemplos completos de uso.
