import { z } from 'zod';
import dotenv from 'dotenv';
import path from 'path';

dotenv.config({ path: path.resolve(process.cwd(), '.env') });

// Environment validation schema
const envSchema = z.object({
  // Application
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().default('3000').transform(Number),
  COOKIE_SECRET: z.string().min(32, 'COOKIE_SECRET must be at least 32 characters').default('your-super-secret-cookie-key-change-in-production-minimum-32-characters'),
  
  // Database
  DB_HOST: z.string().default('localhost'),
  DB_PORT: z.string().default('5432').transform(Number),
  DB_NAME: z.string().default('fastwhite'),
  DB_USER: z.string().default('postgres'),
  DB_PASSWORD: z.string().default('postgres123'),
  DB_SSL: z.string().default('false').transform(val => val === 'true'),
  
  // Authentication & Security
  JWT_SECRET: z.string().min(32, 'JWT_SECRET must be at least 32 characters').default('your-super-secret-jwt-key-change-in-production-minimum-32-characters'),
  JWT_EXPIRES_IN: z.custom<import('ms').StringValue>(
    (val) => {
      try {
        return typeof val === 'string' && !!val && typeof require('ms')(val) === 'number';
      } catch {
        return false;
      }
    },
    { message: 'JWT_EXPIRES_IN must be a valid ms string (e.g., "1h", "7d")' }
  ).default('1h'),
  JWT_REFRESH_EXPIRES_IN: z.custom<import('ms').StringValue>(
    (val) => {
      try {
        return typeof val === 'string' && !!val && typeof require('ms')(val) === 'number';
      } catch {
        return false;
      }
    },
    { message: 'JWT_REFRESH_EXPIRES_IN must be a valid ms string (e.g., "7d")' }
  ).default('7d'),
  BCRYPT_ROUNDS: z.string().default('10').transform(Number),
  
  // Development
  DEBUG: z.string().default('false').transform(val => val === 'true'),
  CORS_ENABLED: z.string().default('true').transform(val => val === 'true'),
  RATE_LIMIT: z.string().default('100').transform(Number),
});

// Validate environment variables
const parseEnv = () => {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      throw new Error(`Environment validation failed:\n${missingVars.join('\n')}`);
    }
    throw error;
  }
};

// Export validated environment variables
export const env = parseEnv();

// Export types for TypeScript
export type Env = z.infer<typeof envSchema>;

// Helper to check if we're in development
export const isDevelopment = env.NODE_ENV === 'development';
export const isProduction = env.NODE_ENV === 'production';
export const isTest = env.NODE_ENV === 'test';

// Database URL builder
export const getDatabaseUrl = () => {
  return `postgresql://${env.DB_USER}:${env.DB_PASSWORD}@${env.DB_HOST}:${env.DB_PORT}/${env.DB_NAME}`;
}; 