import Fastify from 'fastify';
import supertest, { SuperAgentTest } from 'supertest';
import { env } from '../../../../shared/config/env';
import { registerUserRoutes } from '../../routes/user.routes';
import { registerAuthRoutes } from '../../../auth/routes/auth.routes';
import fastifyCookie from '@fastify/cookie';

// Helper para criar app isolado para teste
function buildTestApp() {
  const app = Fastify();
  app.register(fastifyCookie, { secret: env.COOKIE_SECRET, parseOptions: {} });
  app.register(registerUserRoutes, { prefix: '/api/users' });
  app.register(registerAuthRoutes, { prefix: '/api/auth' });
  return app;
}

describe('Users E2E', () => {
  const app = buildTestApp();
  let request: ReturnType<typeof supertest>;
  let createdUserId: string;
  let authToken: string; // Novo: armazenar o token JWT
  const adminCredentials = { email: '<EMAIL>', password: 'admin123' };

  beforeAll(async () => {
    await app.ready();
    request = supertest(app.server);
  });

  afterAll(async () => {
    await app.close();
  });

  it('should create a user', async () => {
    const uniqueSuffix = Date.now();
    const uniqueEmail = `testuser+${uniqueSuffix}@example.com`;
    const uniqueUsername = `testuser${uniqueSuffix}`;
    const password = 'testpassword';
    const res = await request.post('/api/users').send({
      name: 'Test User',
      username: uniqueUsername,
      email: uniqueEmail,
      password,
    });
    expect(res.status).toBe(201);
    expect(res.body.success).toBe(true);
    expect(res.body.data).toHaveProperty('id');
    createdUserId = res.body.data.id;
  });

  it('should login as admin and get JWT token', async () => {
    const res = await request.post('/api/auth/login').send(adminCredentials);
    if (res.status !== 200) {
      // opcional: pode adicionar log aqui se quiser depurar no futuro
    }
    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
    expect(res.body).toHaveProperty('token');
    authToken = res.body.token;
  });

  it('should list users', async () => {
    const res = await request.get('/api/users').set('Authorization', `Bearer ${authToken}`);
    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
    expect(Array.isArray(res.body.data.data)).toBe(true);
  });

  it('should get user by id', async () => {
    const res = await request.get(`/api/users/${createdUserId}`).set('Authorization', `Bearer ${authToken}`);
    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
    expect(res.body.data).toHaveProperty('id', createdUserId);
  });

  it('should update user', async () => {
    const uniqueSuffix = Date.now();
    const updatedUsername = `updateduser${uniqueSuffix}`;
    const res = await request.put(`/api/users/${createdUserId}`)
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        name: 'Updated User',
        username: updatedUsername,
      });
    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
    expect(res.body.data).toHaveProperty('name', 'Updated User');
  });

  it('should delete user', async () => {
    const res = await request.delete(`/api/users/${createdUserId}`).set('Authorization', `Bearer ${authToken}`);
    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
  });

  it('should filter users by isActive', async () => {
    const res = await request.get('/api/users?isActive=true').set('Authorization', `Bearer ${authToken}`);
    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
    if (Array.isArray(res.body.data.data)) {
      for (const user of res.body.data.data) {
        expect(user.isActive).toBe(true);
      }
    }
  });

  it('should filter users by search', async () => {
    // Cria um usuário com nome único para garantir o filtro
    const uniqueSuffix = Date.now();
    const name = `SearchUser${uniqueSuffix}`;
    const email = `searchuser${uniqueSuffix}@example.com`;
    const username = `searchuser${uniqueSuffix}`;
    await request.post('/api/users').send({
      name,
      username,
      email,
      password: 'testpassword',
    });
    const res = await request.get(`/api/users?search=${name}`).set('Authorization', `Bearer ${authToken}`);
    expect(res.status).toBe(200);
    expect(res.body.success).toBe(true);
    expect(Array.isArray(res.body.data.data)).toBe(true);
    expect(res.body.data.data.some((u: any) => u.name === name)).toBe(true);
  });
});