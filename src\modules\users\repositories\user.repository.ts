import { eq, and, or, like, count } from 'drizzle-orm';
import { users } from '../entites/user.entity';
import { db } from '../../../shared/database/connection';
import type {
  User,
  NewUser,
  DatabaseUser,
  NewDatabaseUser,
  UserFilters,
  PaginationOptions,
  PaginatedResult,
} from '../types/user.types';


const toDomainUser = (dbUser: DatabaseUser): User => ({
  id: dbUser.id,
  email: dbUser.email,
  username: dbUser.username,
  password: dbUser.password,
  name: dbUser.name,
  role: dbUser.role as 'admin' | 'user',
  isActive: Bo<PERSON>an(dbUser.isActive),
  createdAt: dbUser.createdAt?.toISOString() || '',
  updatedAt: dbUser.updatedAt?.toISOString() || '',
});

export const create = async (
  userData: Omit<NewUser, 'id' | 'createdAt' | 'updatedAt'>
): Promise<User> => {
  const [dbUser] = await db
    .insert(users)
    .values({
      ...userData,
      role: userData.role || 'user',
      isActive: userData.isActive ?? true,
    } as NewDatabaseUser)
    .returning();
  
  return toDomainUser(dbUser);
};

export const getById = async (id: number): Promise<User | null> => {
  const [dbUser] = await db
    .select()
    .from(users)
    .where(eq(users.id, id))
    .limit(1);

  return dbUser ? toDomainUser(dbUser) : null;
};

export const getAll = async (
  filters: UserFilters = {},
  pagination: PaginationOptions = { page: 1, limit: 10 }
): Promise<PaginatedResult<User>> => {
  const { search, role, isActive } = filters;
  const { page, limit } = pagination;
  const offset = (page - 1) * limit;

  const conditions = [];

  search && conditions.push(
    or(
      like(users.email, `%${search}%`),
      like(users.username, `%${search}%`),
      like(users.name, `%${search}%`)
    )
  );

  role && conditions.push(eq(users.role, role));

  typeof isActive === 'boolean' && conditions.push(eq(users.isActive, isActive));

  const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

  const [[{ total }], dbUsers] = await Promise.all([
    db.select({ total: count() }).from(users).where(whereClause),
    db.select().from(users).where(whereClause).limit(limit).offset(offset).orderBy(users.createdAt),
  ]);

  return {
    data: dbUsers.map(toDomainUser),
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
  };
};

export const update = async (
  id: number,
  userData: Partial<Omit<User, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<User | null> => {
  const [dbUser] = await db
    .update(users)
    .set({
      ...userData,
      updatedAt: new Date(),
    })
    .where(eq(users.id, id))
    .returning();

  return dbUser ? toDomainUser(dbUser) : null;
};

export const softDelete = async (id: number): Promise<boolean> => {
  const result = await db
    .update(users)
    .set({
      isActive: false,
      updatedAt: new Date(),
    })
    .where(eq(users.id, id));

  return (result.rowCount ?? 0) > 0;
};

export const getByEmailOrUsername = async (
  email?: string,
  username?: string
): Promise<User | null> => {
  const conditions = [];
  
  if (email) {
    conditions.push(eq(users.email, email));
  }
  
  if (username) {
    conditions.push(eq(users.username, username));
  }

  const [dbUser] = await db
    .select()
    .from(users)
    .where(and(...conditions))
    .limit(1);

  return dbUser ? toDomainUser(dbUser) : null;
}