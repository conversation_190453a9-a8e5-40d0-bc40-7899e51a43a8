type FailSafeResult<T> = [Error | null, T | null];

/**
 * Decorator que transforma uma função SÍNCRONA em fail-safe
 * Retorna tupla [error, result] ao invés de throw
 * Loga automaticamente os erros no catch
 */
export function failSafe<TArgs extends any[], TReturn>(
  fn: (...args: TArgs) => TReturn
): (...args: TArgs) => FailSafeResult<TReturn> {
  return (...args: TArgs) => {
    try {
      const result = fn(...args);
      return [null, result] as FailSafeResult<TReturn>;
    } catch (error: any) {
      console.error('🚨 FailSafe caught error:', error);
      const errorInstance = error instanceof Error ? error : new Error(String(error));
      return [errorInstance, null] as FailSafeResult<TReturn>;
    }
  };
}

/**
 * Decorator que transforma uma função ASSÍNCRONA em fail-safe
 * Retorna tupla [error, result] ao invés de throw
 * Loga automaticamente os erros no catch
 */
export function asyncFailSafe<TArgs extends any[], TReturn>(
  fn: (...args: TArgs) => Promise<TReturn>
): (...args: TArgs) => Promise<FailSafeResult<TReturn>> {
  return async (...args: TArgs) => {
    try {
      const result = await fn(...args);
      return [null, result] as FailSafeResult<TReturn>;
    } catch (error: any) {
      console.error('🚨 AsyncFailSafe caught error:', error);
      const errorInstance = error instanceof Error ? error : new Error(String(error));
      return [errorInstance, null] as FailSafeResult<TReturn>;
    }
  };
}

/**
 * Tipo helper para extrair o tipo de retorno de uma função fail-safe
 */
export type FailSafeReturn<T> = T extends (...args: any[]) => FailSafeResult<infer U>
  ? U
  : T extends (...args: any[]) => Promise<FailSafeResult<infer U>>
  ? U
  : never;

/**
 * Helper para verificar se uma operação fail-safe teve sucesso
 */
export function isSuccess<T>(result: FailSafeResult<T>): result is [null, T] {
  return result[0] === null;
}

/**
 * Helper para verificar se uma operação fail-safe teve erro
 */
export function isError<T>(result: FailSafeResult<T>): result is [Error, null] {
  return result[0] !== null;
} 