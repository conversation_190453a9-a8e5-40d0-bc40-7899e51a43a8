import { FastifyRequest, FastifyReply } from 'fastify';
import * as nasaService from '../service/nasa.service';
import { HttpStatus } from '../../../shared/enums/status-code';

export const sendApodEmail = async (request: FastifyRequest, reply: FastifyReply) => {
  const { email } = request.body as { email: string };
  await nasaService.enqueueApodEmailJob(email);

  return reply.status(HttpStatus.ACCEPTED).send({
    success: true,
    message: 'Job enfileirado para envio do APOD por email.',
  });
}; 