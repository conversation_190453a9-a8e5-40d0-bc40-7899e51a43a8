import { FastifyRequest } from 'fastify'
import { z, ZodSchema } from 'zod'

/**
 * Validates request body using a Zod schema and returns the validated data
 */
export const validateBody = <T>(req: FastifyRequest) => ({
  with: <U extends ZodSchema<T>>(schema: U): z.infer<U> => schema.parse(req.body)
})

/**
 * Validates request query parameters using a Zod schema and returns the validated data
 */
export const validateQuery = <T>(req: FastifyRequest) => ({
  with: <U extends ZodSchema<T>>(schema: U): z.infer<U> => schema.parse(req.query)
})

/**
 * Validates request parameters using a Zod schema and returns the validated data
 */
export const validateParams = <T>(req: FastifyRequest) => ({
  with: <U extends ZodSchema<T>>(schema: U): z.infer<U> => schema.parse(req.params)
})

/**
 * Validates request headers using a Zod schema and returns the validated data
 */
export const validateHeaders = <T>(req: FastifyRequest) => ({
  with: <U extends ZodSchema<T>>(schema: U): z.infer<U> => schema.parse(req.headers)
})