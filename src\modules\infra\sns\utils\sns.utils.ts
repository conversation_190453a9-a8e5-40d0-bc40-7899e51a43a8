import type {
  SnsNotification,
  SnsSubscriptionConfirmation,
} from '../types/sns.types';

/**
 * Valida se uma notificação SNS tem a estrutura correta
 */
export function isValidSnsNotification(
  payload: any
): payload is SnsNotification {
  return (
    payload &&
    typeof payload === 'object' &&
    payload.Type === 'Notification' &&
    typeof payload.MessageId === 'string' &&
    typeof payload.TopicArn === 'string' &&
    typeof payload.Message === 'string' &&
    typeof payload.Timestamp === 'string'
  );
}

/**
 * Valida se uma confirmação de subscrição SNS tem a estrutura correta
 */
export function isValidSnsSubscriptionConfirmation(
  payload: any
): payload is SnsSubscriptionConfirmation {
  return (
    payload &&
    typeof payload === 'object' &&
    payload.Type === 'SubscriptionConfirmation' &&
    typeof payload.MessageId === 'string' &&
    typeof payload.TopicArn === 'string' &&
    typeof payload.Token === 'string' &&
    typeof payload.SubscribeURL === 'string'
  );
}

/**
 * Extrai o nome do tópico a partir do ARN
 */
export function extractTopicNameFromArn(topicArn: string): string {
  const parts = topicArn.split(':');
  return parts[parts.length - 1];
}

/**
 * Cria atributos de mensagem para SNS
 */
export function createMessageAttributes(
  attributes: Record<string, string | number | boolean>
) {
  const messageAttributes: Record<string, any> = {};

  for (const [key, value] of Object.entries(attributes)) {
    if (typeof value === 'string') {
      messageAttributes[key] = {
        DataType: 'String',
        StringValue: value,
      };
    } else if (typeof value === 'number') {
      messageAttributes[key] = {
        DataType: 'Number',
        StringValue: value.toString(),
      };
    } else if (typeof value === 'boolean') {
      messageAttributes[key] = {
        DataType: 'String',
        StringValue: value.toString(),
      };
    }
  }

  return messageAttributes;
}

/**
 * Formata uma mensagem estruturada para SNS
 */
export function formatStructuredMessage(data: {
  eventType: string;
  timestamp?: string;
  source?: string;
  version?: string;
  data: Record<string, any>;
}) {
  return JSON.stringify({
    eventType: data.eventType,
    timestamp: data.timestamp || new Date().toISOString(),
    source: data.source || 'unknown',
    version: data.version || '1.0.0',
    data: data.data,
  });
}

/**
 * Cria um identificador único para mensagens em lote
 */
export function generateBatchMessageId(prefix: string = 'msg'): string {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Valida ARN do tópico SNS
 */
export function isValidTopicArn(arn: string): boolean {
  const arnPattern = /^arn:aws:sns:[a-z0-9-]+:\d{12}:[a-zA-Z0-9_-]+$/;
  return arnPattern.test(arn);
}

/**
 * Valida ARN de subscrição SNS
 */
export function isValidSubscriptionArn(arn: string): boolean {
  const arnPattern =
    /^arn:aws:sns:[a-z0-9-]+:\d{12}:[a-zA-Z0-9_-]+:[a-f0-9-]+$/;
  return arnPattern.test(arn);
}

/**
 * Extrai região do ARN
 */
export function extractRegionFromArn(arn: string): string {
  const parts = arn.split(':');
  return parts[3] || '';
}

/**
 * Extrai account ID do ARN
 */
export function extractAccountIdFromArn(arn: string): string {
  const parts = arn.split(':');
  return parts[4] || '';
}
