export const HttpErrors = {
  // 1xx Informational
  CONTINUE: {
    status: 100,
    message: 'Your request is being processed. Please wait.',
  },
  SWITCHING_PROTOCOLS: {
    status: 101,
    message: 'The server is switching to the requested protocol.',
  },
  PROCESSING: {
    status: 102,
    message: 'Your request is being processed. This may take a moment.',
  },

  // 2xx Success
  SUCCESS: {
    status: 200,
    message: 'Request completed successfully.',
  },
  CREATED: {
    status: 201,
    message: 'Resource created successfully.',
  },
  ACCEPTED: {
    status: 202,
    message: 'Request accepted and is being processed.',
  },
  NO_CONTENT: {
    status: 204,
    message: 'Request completed successfully with no content to return.',
  },
  RESET_CONTENT: {
    status: 205,
    message: 'Request completed successfully. Please reset your form.',
  },
  PARTIAL_CONTENT: {
    status: 206,
    message: 'Partial content delivered as requested.',
  },

  // 3xx Redirection
  MULTIPLE_CHOICES: {
    status: 300,
    message: 'Multiple options are available for this resource.',
  },
  MOVED_PERMANENTLY: {
    status: 301,
    message: 'The resource has been permanently moved to a new location.',
  },
  FOUND: {
    status: 302,
    message: 'The resource has been temporarily moved to a different location.',
  },
  SEE_OTHER: {
    status: 303,
    message: 'Please check the alternative location for this resource.',
  },
  NOT_MODIFIED: {
    status: 304,
    message: 'The resource has not been modified since your last request.',
  },
  TEMPORARY_REDIRECT: {
    status: 307,
    message: 'The resource is temporarily available at a different location.',
  },
  PERMANENT_REDIRECT: {
    status: 308,
    message: 'The resource has been permanently moved to a new location.',
  },

  // 4xx Client Errors
  BAD_REQUEST: {
    status: 400,
    message:
      'Your request contains invalid data or is malformed. Please check and try again.',
  },
  UNAUTHORIZED: {
    status: 401,
    message:
      'You need to be authenticated to access this resource. Please log in.',
  },
  PAYMENT_REQUIRED: {
    status: 402,
    message: 'Payment is required to access this resource.',
  },
  FORBIDDEN: {
    status: 403,
    message: 'You do not have permission to access this resource.',
  },
  NOT_FOUND: {
    status: 404,
    message:
      'The requested resource could not be found. Please check the URL and try again.',
  },
  METHOD_NOT_ALLOWED: {
    status: 405,
    message: 'The requested method is not allowed for this resource.',
  },
  NOT_ACCEPTABLE: {
    status: 406,
    message:
      'The server cannot provide a response in the format you requested.',
  },
  PROXY_AUTHENTICATION_REQUIRED: {
    status: 407,
    message: 'Proxy authentication is required to access this resource.',
  },
  REQUEST_TIMEOUT: {
    status: 408,
    message: 'Your request took too long to process. Please try again.',
  },
  CONFLICT: {
    status: 409,
    message: 'Your request conflicts with the current state of the resource.',
  },
  GONE: {
    status: 410,
    message:
      'The requested resource is no longer available and will not be back.',
  },
  LENGTH_REQUIRED: {
    status: 411,
    message: 'Content length is required for this request.',
  },
  PRECONDITION_FAILED: {
    status: 412,
    message: 'One or more preconditions in your request were not met.',
  },
  PAYLOAD_TOO_LARGE: {
    status: 413,
    message:
      'The request payload is too large. Please reduce the size and try again.',
  },
  URI_TOO_LONG: {
    status: 414,
    message: 'The request URL is too long. Please shorten it and try again.',
  },
  UNSUPPORTED_MEDIA_TYPE: {
    status: 415,
    message: 'The media type of your request is not supported.',
  },
  RANGE_NOT_SATISFIABLE: {
    status: 416,
    message: 'The requested range cannot be satisfied.',
  },
  EXPECTATION_FAILED: {
    status: 417,
    message:
      'The server cannot meet the requirements specified in your request.',
  },
  IM_A_TEAPOT: {
    status: 418,
    message: "I'm a teapot and cannot brew coffee.",
  },
  MISDIRECTED_REQUEST: {
    status: 421,
    message: 'Your request was directed to a server that cannot respond.',
  },
  UNPROCESSABLE_ENTITY: {
    status: 422,
    message: 'Your request is well-formed but contains semantic errors.',
  },
  LOCKED: {
    status: 423,
    message: 'The resource you are trying to access is locked.',
  },
  FAILED_DEPENDENCY: {
    status: 424,
    message: 'The request failed due to a dependency issue.',
  },
  TOO_EARLY: {
    status: 425,
    message: 'Your request was sent too early. Please wait and try again.',
  },
  UPGRADE_REQUIRED: {
    status: 426,
    message: 'You need to upgrade your client to access this resource.',
  },
  PRECONDITION_REQUIRED: {
    status: 428,
    message: 'This request requires certain preconditions to be met.',
  },
  TOO_MANY_REQUESTS: {
    status: 429,
    message:
      'You have sent too many requests. Please wait before trying again.',
  },
  REQUEST_HEADER_FIELDS_TOO_LARGE: {
    status: 431,
    message:
      'Your request headers are too large. Please reduce them and try again.',
  },
  UNAVAILABLE_FOR_LEGAL_REASONS: {
    status: 451,
    message: 'This resource is unavailable for legal reasons.',
  },

  // 5xx Server Errors
  INTERNAL_SERVER_ERROR: {
    status: 500,
    message:
      'An unexpected error occurred on our server. Please try again later.',
  },
  NOT_IMPLEMENTED: {
    status: 501,
    message: 'This functionality is not yet implemented on our server.',
  },
  BAD_GATEWAY: {
    status: 502,
    message:
      'We received an invalid response from an upstream server. Please try again later.',
  },
  SERVICE_UNAVAILABLE: {
    status: 503,
    message: 'Our service is temporarily unavailable. Please try again later.',
  },
  GATEWAY_TIMEOUT: {
    status: 504,
    message:
      'Our server did not receive a timely response. Please try again later.',
  },
  HTTP_VERSION_NOT_SUPPORTED: {
    status: 505,
    message: 'The HTTP version you are using is not supported.',
  },
  VARIANT_ALSO_NEGOTIATES: {
    status: 506,
    message: 'There is a configuration error on our server.',
  },
  INSUFFICIENT_STORAGE: {
    status: 507,
    message:
      'Our server does not have enough storage to complete your request.',
  },
  LOOP_DETECTED: {
    status: 508,
    message: 'We detected an infinite loop while processing your request.',
  },
  NOT_EXTENDED: {
    status: 510,
    message: 'Further extensions are required to fulfill your request.',
  },
  NETWORK_AUTHENTICATION_REQUIRED: {
    status: 511,
    message: 'Network authentication is required to access this resource.',
  },
} as const;
