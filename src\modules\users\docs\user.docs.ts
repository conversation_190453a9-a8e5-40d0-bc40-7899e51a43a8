/**
 * Complete Swagger Documentation for User API
 * Manual Swagger definitions that work with Zod validation
 */

// Manual Swagger definitions (no validation, just documentation)
export const userSwaggerPaths = {
  '/api/users': {
    post: {
      tags: ['Users'],
      summary: 'Create User',
      description: 'Create a new user account',
      consumes: ['application/json'],
      produces: ['application/json'],
      parameters: [
        {
          name: 'body',
          in: 'body',
          required: true,
          schema: {
            type: 'object',
            required: ['email', 'username', 'password', 'name'],
            properties: {
              email: {
                type: 'string',
                format: 'email',
                example: '<EMAIL>'
              },
              username: {
                type: 'string',
                minLength: 3,
                maxLength: 50,
                example: 'john_doe123'
              },
              password: {
                type: 'string',
                minLength: 8,
                example: 'MySecurePass123!'
              },
              name: {
                type: 'string',
                minLength: 2,
                maxLength: 100,
                example: '<PERSON>'
              },
              role: {
                type: 'string',
                enum: ['USER', 'ADMIN'],
                default: 'USER',
                example: 'USER'
              }
            },
            example: {
              email: '<EMAIL>',
              username: 'john_doe123',
              password: 'MySecurePass123!',
              name: 'John Doe',
              role: 'USER'
            }
          }
        }
      ],
      responses: {
        201: {
          description: 'User created successfully',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              message: { type: 'string', example: 'User created successfully' },
              data: { $ref: '#/definitions/User' }
            }
          }
        },
        400: {
          description: 'Validation failed',
          schema: { $ref: '#/definitions/ValidationError' }
        },
        409: {
          description: 'User already exists',
          schema: { $ref: '#/definitions/ErrorResponse' }
        }
      }
    },
    get: {
      tags: ['Users'],
      summary: 'Get All Users',
      description: 'Get all users with optional filtering',
      produces: ['application/json'],
      parameters: [
        {
          name: 'role',
          in: 'query',
          type: 'string',
          enum: ['USER', 'ADMIN'],
          description: 'Filter by user role'
        },
        {
          name: 'isActive',
          in: 'query',
          type: 'boolean',
          description: 'Filter by active status'
        },
        {
          name: 'search',
          in: 'query',
          type: 'string',
          description: 'Search in name, email, or username'
        }
      ],
      responses: {
        200: {
          description: 'Users retrieved successfully',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              message: { type: 'string', example: 'Users retrieved successfully' },
              data: {
                type: 'array',
                items: { $ref: '#/definitions/User' }
              }
            }
          }
        }
      }
    }
  },
  '/api/users/{id}': {
    get: {
      tags: ['Users'],
      summary: 'Get User by ID',
      description: 'Get a specific user by ID',
      produces: ['application/json'],
      parameters: [
        {
          name: 'id',
          in: 'path',
          required: true,
          type: 'string',
          pattern: '^[1-9]\\d*$',
          description: 'User ID'
        }
      ],
      responses: {
        200: {
          description: 'User found successfully',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              message: { type: 'string', example: 'User found successfully' },
              data: { $ref: '#/definitions/User' }
            }
          }
        },
        400: {
          description: 'Invalid ID format',
          schema: { $ref: '#/definitions/ValidationError' }
        },
        404: {
          description: 'User not found',
          schema: { $ref: '#/definitions/ErrorResponse' }
        }
      }
    },
    put: {
      tags: ['Users'],
      summary: 'Update User',
      description: 'Update user information (partial update)',
      consumes: ['application/json'],
      produces: ['application/json'],
      parameters: [
        {
          name: 'id',
          in: 'path',
          required: true,
          type: 'string',
          pattern: '^[1-9]\\d*$',
          description: 'User ID'
        },
        {
          name: 'body',
          in: 'body',
          required: true,
          schema: {
            type: 'object',
            properties: {
              email: {
                type: 'string',
                format: 'email',
                example: '<EMAIL>'
              },
              username: {
                type: 'string',
                minLength: 3,
                maxLength: 50,
                example: 'john_updated'
              },
              password: {
                type: 'string',
                minLength: 8,
                example: 'NewSecurePass456!'
              },
              name: {
                type: 'string',
                minLength: 2,
                maxLength: 100,
                example: 'John Smith'
              },
              role: {
                type: 'string',
                enum: ['USER', 'ADMIN'],
                example: 'ADMIN'
              },
              isActive: {
                type: 'boolean',
                example: false
              }
            },
            example: {
              name: 'John Smith',
              email: '<EMAIL>',
              role: 'ADMIN'
            }
          }
        }
      ],
      responses: {
        200: {
          description: 'User updated successfully',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              message: { type: 'string', example: 'User updated successfully' },
              data: { $ref: '#/definitions/User' }
            }
          }
        },
        404: {
          description: 'User not found',
          schema: { $ref: '#/definitions/ErrorResponse' }
        }
      }
    },
    delete: {
      tags: ['Users'],
      summary: 'Delete User',
      description: 'Soft delete a user (sets isActive to false)',
      produces: ['application/json'],
      parameters: [
        {
          name: 'id',
          in: 'path',
          required: true,
          type: 'string',
          pattern: '^[1-9]\\d*$',
          description: 'User ID'
        }
      ],
      responses: {
        200: {
          description: 'User deleted successfully',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              message: { type: 'string', example: 'User deleted successfully' }
            }
          }
        },
        404: {
          description: 'User not found',
          schema: { $ref: '#/definitions/ErrorResponse' }
        }
      }
    }
  }
};

// Swagger schemas for routes (only examples, no validation)
export const userRouteSchemas = {
  createUser: {
    description: 'Create a new user account',
    tags: ['Users'],
    summary: 'Create User',
    body: {
      type: 'object',
      example: {
        email: '<EMAIL>',
        username: 'john_doe123',
        password: 'MySecurePass123!',
        name: 'John Doe',
        role: 'USER'
      }
    },
    response: {
      201: {
        description: 'User created successfully',
        type: 'object'
      },
      400: {
        description: 'Validation failed',
        type: 'object'
      }
    }
  },

  getAllUsers: {
    description: 'Get all users with optional filtering',
    tags: ['Users'],
    summary: 'Get All Users',
    querystring: {
      type: 'object',
      properties: {
        role: { type: 'string' },
        isActive: { type: 'boolean' },
        search: { type: 'string' }
      }
    }
  },

  getUserById: {
    description: 'Get a specific user by ID',
    tags: ['Users'],
    summary: 'Get User by ID',
    params: {
      type: 'object',
      properties: {
        id: { type: 'string' }
      }
    }
  },

  updateUser: {
    description: 'Update user information (partial update)',
    tags: ['Users'],
    summary: 'Update User',
    params: {
      type: 'object',
      properties: {
        id: { type: 'string' }
      }
    },
    body: {
      type: 'object',
      example: {
        name: 'John Smith',
        email: '<EMAIL>',
        role: 'ADMIN'
      }
    }
  },

  deleteUser: {
    description: 'Soft delete a user (sets isActive to false)',
    tags: ['Users'],
    summary: 'Delete User',
    params: {
      type: 'object',
      properties: {
        id: { type: 'string' }
      }
    }
  }
};

export const userSwaggerDocs = {
  // ===== CREATE USER =====
  createUser: {
    description: 'Create a new user account',
    tags: ['Users'],
    summary: 'Create User',
    body: {
      type: 'object',
      required: ['email', 'username', 'password', 'name'],
      properties: {
        email: {
          type: 'string',
          format: 'email',
          description: 'Valid email address',
          example: '<EMAIL>'
        },
        username: {
          type: 'string',
          minLength: 3,
          maxLength: 50,
          pattern: '^[a-zA-Z0-9_]+$',
          description: 'Unique username (letters, numbers, underscore only)',
          example: 'john_doe123'
        },
        password: {
          type: 'string',
          minLength: 8,
          description: 'Strong password (minimum 8 characters)',
          example: 'MySecurePass123!'
        },
        name: {
          type: 'string',
          minLength: 2,
          maxLength: 100,
          description: 'Full name',
          example: 'John Doe'
        },
        role: {
          type: 'string',
          enum: ['USER', 'ADMIN'],
          default: 'USER',
          description: 'User role',
          example: 'USER'
        }
      },
      example: {
        email: '<EMAIL>',
        username: 'john_doe123',
        password: 'MySecurePass123!',
        name: 'John Doe',
        role: 'USER'
      }
    },
    response: {
      201: {
        description: 'User created successfully',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: true },
          message: { type: 'string', example: 'User created successfully' },
          data: {
            type: 'object',
            properties: {
              id: { type: 'number', example: 1 },
              email: { type: 'string', example: '<EMAIL>' },
              username: { type: 'string', example: 'john_doe123' },
              name: { type: 'string', example: 'John Doe' },
              role: { type: 'string', example: 'USER' },
              isActive: { type: 'boolean', example: true },
              createdAt: { type: 'string', format: 'date-time', example: '2024-01-15T10:30:00.000Z' },
              updatedAt: { type: 'string', format: 'date-time', example: '2024-01-15T10:30:00.000Z' }
            }
          }
        }
      },
      400: {
        description: 'Validation failed',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: false },
          message: { type: 'string', example: 'Validation failed' },
          errors: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                field: { type: 'string', example: 'password' },
                message: { type: 'string', example: 'String must contain at least 8 character(s)' },
                code: { type: 'string', example: 'too_small' },
                minimum: { type: 'number', example: 8 },
                type: { type: 'string', example: 'string' }
              }
            }
          },
          details: { type: 'string', example: '1 validation error(s) found' }
        }
      },
      409: {
        description: 'User already exists',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: false },
          message: { type: 'string', example: 'User with this email or username already exists' }
        }
      }
    }
  },

  // ===== GET ALL USERS =====
  getAllUsers: {
    description: 'Get all users with optional filtering',
    tags: ['Users'],
    summary: 'Get All Users',
    querystring: {
      type: 'object',
      properties: {
        role: {
          type: 'string',
          enum: ['USER', 'ADMIN'],
          description: 'Filter by user role',
          example: 'USER'
        },
        isActive: {
          type: 'boolean',
          description: 'Filter by active status',
          example: true
        },
        search: {
          type: 'string',
          description: 'Search in name, email, or username',
          example: 'john'
        }
      },
      example: {
        role: 'USER',
        isActive: true,
        search: 'john'
      }
    },
    response: {
      200: {
        description: 'Users retrieved successfully',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: true },
          message: { type: 'string', example: 'Users retrieved successfully' },
          data: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'number', example: 1 },
                email: { type: 'string', example: '<EMAIL>' },
                username: { type: 'string', example: 'john_doe123' },
                name: { type: 'string', example: 'John Doe' },
                role: { type: 'string', example: 'USER' },
                isActive: { type: 'boolean', example: true },
                createdAt: { type: 'string', format: 'date-time', example: '2024-01-15T10:30:00.000Z' },
                updatedAt: { type: 'string', format: 'date-time', example: '2024-01-15T10:30:00.000Z' }
              }
            }
          }
        }
      }
    }
  },

  // ===== GET USER BY ID =====
  getUserById: {
    description: 'Get a specific user by ID',
    tags: ['Users'],
    summary: 'Get User by ID',
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: {
          type: 'string',
          pattern: '^[1-9]\\d*$',
          description: 'User ID (positive integer)',
          example: '1'
        }
      }
    },
    response: {
      200: {
        description: 'User found successfully',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: true },
          message: { type: 'string', example: 'User found successfully' },
          data: {
            type: 'object',
            properties: {
              id: { type: 'number', example: 1 },
              email: { type: 'string', example: '<EMAIL>' },
              username: { type: 'string', example: 'john_doe123' },
              name: { type: 'string', example: 'John Doe' },
              role: { type: 'string', example: 'USER' },
              isActive: { type: 'boolean', example: true },
              createdAt: { type: 'string', format: 'date-time', example: '2024-01-15T10:30:00.000Z' },
              updatedAt: { type: 'string', format: 'date-time', example: '2024-01-15T10:30:00.000Z' }
            }
          }
        }
      },
      400: {
        description: 'Invalid ID format',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: false },
          message: { type: 'string', example: 'Validation failed' },
          errors: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                field: { type: 'string', example: 'id' },
                message: { type: 'string', example: 'Invalid' },
                code: { type: 'string', example: 'invalid_string' },
                validation: { type: 'string', example: 'regex' }
              }
            }
          },
          details: { type: 'string', example: '1 validation error(s) found' }
        }
      },
      404: {
        description: 'User not found',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: false },
          message: { type: 'string', example: 'User not found' }
        }
      }
    }
  },

  // ===== UPDATE USER =====
  updateUser: {
    description: 'Update user information (partial update)',
    tags: ['Users'],
    summary: 'Update User',
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: {
          type: 'string',
          pattern: '^[1-9]\\d*$',
          description: 'User ID (positive integer)',
          example: '1'
        }
      }
    },
    body: {
      type: 'object',
      properties: {
        email: {
          type: 'string',
          format: 'email',
          description: 'New email address',
          example: '<EMAIL>'
        },
        username: {
          type: 'string',
          minLength: 3,
          maxLength: 50,
          pattern: '^[a-zA-Z0-9_]+$',
          description: 'New username',
          example: 'john_updated'
        },
        password: {
          type: 'string',
          minLength: 8,
          description: 'New password',
          example: 'NewSecurePass456!'
        },
        name: {
          type: 'string',
          minLength: 2,
          maxLength: 100,
          description: 'New full name',
          example: 'John Smith'
        },
        role: {
          type: 'string',
          enum: ['USER', 'ADMIN'],
          description: 'New user role',
          example: 'ADMIN'
        },
        isActive: {
          type: 'boolean',
          description: 'Active status',
          example: false
        }
      },
      example: {
        name: 'John Smith',
        email: '<EMAIL>',
        role: 'ADMIN'
      }
    },
    response: {
      200: {
        description: 'User updated successfully',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: true },
          message: { type: 'string', example: 'User updated successfully' },
          data: {
            type: 'object',
            properties: {
              id: { type: 'number', example: 1 },
              email: { type: 'string', example: '<EMAIL>' },
              username: { type: 'string', example: 'john_doe123' },
              name: { type: 'string', example: 'John Smith' },
              role: { type: 'string', example: 'ADMIN' },
              isActive: { type: 'boolean', example: true },
              createdAt: { type: 'string', format: 'date-time', example: '2024-01-15T10:30:00.000Z' },
              updatedAt: { type: 'string', format: 'date-time', example: '2024-01-15T14:45:00.000Z' }
            }
          }
        }
      },
      404: {
        description: 'User not found',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: false },
          message: { type: 'string', example: 'User not found' }
        }
      }
    }
  },

  // ===== DELETE USER =====
  deleteUser: {
    description: 'Soft delete a user (sets isActive to false)',
    tags: ['Users'],
    summary: 'Delete User',
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: {
          type: 'string',
          pattern: '^[1-9]\\d*$',
          description: 'User ID (positive integer)',
          example: '1'
        }
      }
    },
    response: {
      200: {
        description: 'User deleted successfully',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: true },
          message: { type: 'string', example: 'User deleted successfully' }
        }
      },
      404: {
        description: 'User not found',
        type: 'object',
        properties: {
          success: { type: 'boolean', example: false },
          message: { type: 'string', example: 'User not found' }
        }
      }
    }
  }
};

// ===== SWAGGER COMPONENTS =====
export const userSwaggerComponents = {
  schemas: {
    User: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        email: { type: 'string', format: 'email', example: '<EMAIL>' },
        username: { type: 'string', example: 'john_doe123' },
        name: { type: 'string', example: 'John Doe' },
        role: { type: 'string', enum: ['USER', 'ADMIN'], example: 'USER' },
        isActive: { type: 'boolean', example: true },
        createdAt: { type: 'string', format: 'date-time', example: '2024-01-15T10:30:00.000Z' },
        updatedAt: { type: 'string', format: 'date-time', example: '2024-01-15T10:30:00.000Z' }
      }
    },
    ValidationError: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Validation failed' },
        errors: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              field: { type: 'string', example: 'password' },
              message: { type: 'string', example: 'String must contain at least 8 character(s)' },
              code: { type: 'string', example: 'too_small' }
            }
          }
        },
        details: { type: 'string', example: '1 validation error(s) found' }
      }
    },
    ErrorResponse: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Operation failed' }
      }
    }
  }
}; 