version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: fast-white-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME:-fastwhite}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-postgres123}
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - fast-white-network
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U ${DB_USER:-postgres} -d ${DB_NAME:-fastwhite}" ]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis para BullMQ
  redis:
    image: redis:7
    container_name: redis-nasa
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - fast-white-network

  # API Server
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: fast-white-api
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      PORT: ${API_PORT:-3000}
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-fastwhite}
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-postgres123}
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      BCRYPT_ROUNDS: ${BCRYPT_ROUNDS:-10}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      OTEL_EXPORTER_OTLP_ENDPOINT: http://jaeger:4318/v1/traces
    ports:
      - "${API_PORT:-3000}:${API_PORT:-3000}"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - fast-white-network
    volumes:
      - .:/app
      - /app/node_modules
      - /app/dist
      - ./logs:/app/logs
    command: sh -c "pnpm run db:setup && pnpm run dev"
    stdin_open: true
    tty: true

  jaeger:
    image: jaegertracing/all-in-one:1.56
    container_name: jaeger
    ports:
      - "16686:16686" # UI Web
      - "4318:4318" # OTLP HTTP (recebe traces do OpenTelemetry)
    networks:
      - fast-white-network
  # Worker BullMQ para NASA
  worker-nasa:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: worker-nasa
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-fastwhite}
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-postgres123}
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      BCRYPT_ROUNDS: ${BCRYPT_ROUNDS:-10}
      REDIS_HOST: redis
      REDIS_PORT: 6379
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - fast-white-network
    volumes:
      - .:/app
      - /app/node_modules
      - /app/dist
      - ./logs:/app/logs
    command: pnpm run worker:nasa
    stdin_open: true
    tty: true

volumes:
  postgres_data:
    driver: local

networks:
  fast-white-network:
    driver: bridge
