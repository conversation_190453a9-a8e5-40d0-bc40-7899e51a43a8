import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import { env, getDatabaseUrl } from '../config/env';

const pool = new Pool({
  connectionString: getDatabaseUrl(),
  ssl: env.DB_SSL ? { rejectUnauthorized: false } : false,
  // Additional pool configuration
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
});

// Handle pool errors
pool.on('error', (err) => {
  console.error('Unexpected error on idle client', err);
  process.exit(-1);
});

export const db = drizzle(pool);

export type Database = typeof db;

// Helper function to test database connection
export const testConnection = async () => {
  try {
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();
    console.log('✅ Database connected successfully');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}; 