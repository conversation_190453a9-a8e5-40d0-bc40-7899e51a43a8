import type { FastifyInstance } from 'fastify';
import * as userController from '../controllers/user.controller';
import { sanitizeResponse } from '@/shared/middleware/sanitize-response.middleware';
import { verifyOwnershipFromParams } from '@/shared/middleware/verify-ownership-from-params.middlware';
import { verifyToken } from '@/shared/middleware/auth.middleware';
import { idempotencyMiddleware } from '@/shared/middleware/idempotency.middleware';

export const registerUserRoutes = async (fastify: FastifyInstance) => {
  fastify.addHook('onSend', sanitizeResponse());

  fastify.post('/', {
    preHandler: [idempotencyMiddleware], // Colocado apenas para exemplo de funcionamento do middleware
    handler: userController.create,
  });

  fastify.get('/', {
    handler: userController.getAll,
    preHandler: [verifyToken(), verifyOwnershipFromParams()],
  });

  fastify.get('/:id', {
    handler: userController.getById,
    preHandler: [verifyToken(), verifyOwnershipFromParams()],
  });

  fastify.put('/:id', {
    handler: userController.update,
    preHandler: [verifyToken(), verifyOwnershipFromParams()],
  });

  fastify.delete('/:id', {
    handler: userController.softDelete,
    preHandler: [verifyToken(), verifyOwnershipFromParams()],
  });
};
