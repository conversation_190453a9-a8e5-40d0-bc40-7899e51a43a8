import { HttpErrors } from '../enums/errors';
import type { FastifyRequest, FastifyReply } from 'fastify';
import type { User } from '@/modules/users/entites/user.entity';

export const verifyOwnershipFromParams = (
  options?: {
    allowedRoles?: User['role'][];
  }
) => {
  const { allowedRoles = ['admin'] } = options || {};

  return async (
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<void> => {
    const user = request.user;
    const userId = (request.params as { [key: string]: string })['id'] as string;

    if (!user) {
      const responseData = HttpErrors.UNAUTHORIZED;
      return reply.status(responseData.status).send({
        success: false,
        message: responseData.message,
      });
    }

    if (
      userId &&
      userId !== user.userId &&
      !allowedRoles.includes(user.role as User['role'])
    ) {
      const responseData = HttpErrors.FORBIDDEN;
      return reply.status(responseData.status).send({
        success: false,
        message: responseData.message,
      });
    }
  };
};
