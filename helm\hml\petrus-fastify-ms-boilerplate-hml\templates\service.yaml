{{- if not (empty .Values.image.containerPort) }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.name }}-svc
  labels:
    app: {{ .Values.name }}
    helm.sh/chart: "{{.Chart.Name}}-{{.Chart.Version}}"
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.targetPort }}
      targetPort: {{ .Values.image.containerPort }}
  selector:
    app: {{ .Values.name }}-dpl
{{- end }}