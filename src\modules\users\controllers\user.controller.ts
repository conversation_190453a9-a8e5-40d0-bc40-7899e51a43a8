import type { FastifyRequest, FastifyReply } from 'fastify';
import * as userService from '../services/user.service';
import { validateBody, validateQuery, validateParams } from '../../../shared/validators/schema-validator';
import {
  createUserSchema,
  updateUserSchema,
  getUsersQuerySchema,
  userIdSchema,
} from '../zod/user.schema';
import { HttpStatus } from '../../../shared/enums/status-code';
import { sanitizeResponse } from '../../../shared/utils/data-manipulation';

export const create = async (request: FastifyRequest, reply: FastifyReply) => {
  const userData = validateBody(request).with(createUserSchema);
  const user = await userService.create(userData);

  return reply.status(HttpStatus.CREATED).send({
    success: true,
    message: 'User created successfully',
    data: user,
  });
};

export const getById = async (request: FastifyRequest, reply: FastifyReply) => {
  const { id } = validateParams(request).with(userIdSchema);
  const user = await userService.getById(id);

  return reply.send({
    success: true,
    message: 'User retrieved successfully',
    data: user,
  });
};

export const getAll = async (request: FastifyRequest, reply: FastifyReply) => {
  const query = validateQuery(request).with(getUsersQuerySchema);
  const { page, limit, ...filters } = query;
  const pagination = { page, limit };

  const result = await userService.getAll(filters, pagination);

  return reply.send({
    success: true,
    message: 'Users retrieved successfully',
    data: result.data,
    pagination: {
      page: result.page,
      limit: result.limit,
      total: result.total,
      totalPages: result.totalPages,
    },
  });
};

export const update = async (request: FastifyRequest, reply: FastifyReply) => {
  const { id } = validateParams(request).with(userIdSchema);
  const userData = validateBody(request).with(updateUserSchema);
  const user = await userService.update(id, userData);

  return reply.send({
    success: true,
    message: 'User updated successfully',
    data: user,
  });
};

export const softDelete = async (request: FastifyRequest, reply: FastifyReply) => {
  const { id } = validateParams(request).with(userIdSchema);
  await userService.softDelete(id);

  return reply.send({
    success: true,
    message: 'User deleted successfully',
  });
}; 