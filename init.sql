-- Initialize FastWhite Database
-- This script runs when the PostgreSQL container starts for the first time

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create initial database (if not exists)
-- Note: This is handled by POSTGRES_DB environment variable

-- You can add initial data here if needed
-- Example:
-- INSERT INTO users (email, username, password, name, role) 
-- VALUES ('<EMAIL>', 'admin', '$2b$10$...', 'Admin User', 'admin');

-- Log initialization
DO $$
BEGIN
    RAISE NOTICE 'FastWhite database initialized successfully!';
END $$; 