import { z } from 'zod';
import type { users } from '../entites/user.entity';
import type { PaginationOptions, PaginatedResult, ApiResponse } from '../../../shared/types/global.types';
import {
  createUserSchema,
  updateUserSchema,
  getUsersQuerySchema,
  userIdSchema,
} from '../zod/user.schema';

// ============================================================================
// ZOD TYPES
// ============================================================================

export type CreateUserInput = z.infer<typeof createUserSchema>;
export type UpdateUserInput = z.infer<typeof updateUserSchema>;
export type GetUsersQuery = z.infer<typeof getUsersQuerySchema>;
export type UserIdParam = z.infer<typeof userIdSchema>;

// ============================================================================
// DATABASE TYPES
// ============================================================================

export type DatabaseUser = typeof users.$inferSelect;
export type NewDatabaseUser = typeof users.$inferInsert;

// ============================================================================
// SERVICE TYPES
// ============================================================================

export interface User {
  id: number;
  email: string;
  username: string;
  password: string;
  name: string;
  role: 'admin' | 'user';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export type NewUser = Omit<User, 'id' | 'createdAt' | 'updatedAt'>;
export type UserWithoutPassword = Omit<User, 'password'>;

// ============================================================================
// REPOSITORYFILTERS
// ============================================================================

export interface UserFilters {
  search?: string;
  role?: 'admin' | 'user';
  isActive?: boolean;
}

export type { PaginationOptions, PaginatedResult, ApiResponse }; 