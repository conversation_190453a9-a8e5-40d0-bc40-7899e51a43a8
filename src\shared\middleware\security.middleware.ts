import type { FastifyRequest, FastifyReply } from 'fastify';
import DOMPurify from 'isomorphic-dompurify';
import { HttpStatus } from '@/shared/enums/status-code';
import logger from '@/shared/logger/logger';

/**
 * Lista de padrões perigosos para SQL Injection
 */
const SQL_INJECTION_PATTERNS = [
  /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE|UNION|OR|AND)\b)/gi,

  /(--|\#|\/\*|\*\/)/g,

  /[';]|(\|\|)|(\&\&)/g,

  /\b(CAST|CONVERT|SUBSTRING|CHAR|ASCII)\b/gi,

  /0x[0-9a-f]+/gi,

  /\bunion\b.*(select|all)\b/gi,

  /\b(WAITFOR|DELAY|SLEEP|BENCHMARK)\b/gi,
];

/**
 * Lista de padrões perigosos para XSS
 */
const XSS_PATTERNS = [
  /<script[^>]*>.*?<\/script>/gi,

  /on\w+\s*=\s*["'][^"']*["']/gi,

  /javascript\s*:/gi,

  /data\s*:\s*text\/html/gi,

  /expression\s*\(/gi,

  /@import\s+/gi,

  /vbscript\s*:/gi,

  /livescript\s*:/gi,

  /mocha\s*:/gi,
];

/**
 * Lista de padrões perigosos para Path Traversal
 */
const PATH_TRAVERSAL_PATTERNS = [
  /\.\.\//g,
  /\.\.\\/g,
  /\.\.%2f/gi,
  /\.\.%5c/gi,
  /%2e%2e%2f/gi,
  /%2e%2e%5c/gi,
  /\.\.\x2f/g,
  /\.\.\x5c/g,
  /(\/|\\)(etc|windows|system32|boot|proc|var|tmp|home)(\/|\\)/gi,
  /\/(passwd|shadow|hosts|fstab)/gi,
  /\\(boot\.ini|pagefile\.sys|hiberfil\.sys)/gi,
];

/**
 * Campos que devem ser sanitizados para HTML
 */
const HTML_FIELDS = ['content', 'description', 'bio', 'message', 'comment'];

/**
 * Interface para configuração do middleware
 */
interface SecurityMiddlewareOptions {
  enableSqlInjectionProtection?: boolean;
  enableXssProtection?: boolean;
  enablePathTraversalProtection?: boolean;
  enableInputSanitization?: boolean;
  strictMode?: boolean;
  logAttempts?: boolean;
  customPatterns?: RegExp[];
}

/**
 * Sanitiza string removendo caracteres perigosos para SQL
 */
function sanitizeSqlInput(input: string): string {
  if (typeof input !== 'string') return input;

  return input
    .replace(/[';\\]/g, '')
    .replace(/--/g, '')
    .replace(/\/\*|\*\//g, '')
    .replace(/\x00/g, '')
    .trim();
}

/**
 * Sanitiza string removendo código perigoso para XSS
 */
function sanitizeXssInput(input: string): string {
  if (typeof input !== 'string') return input;

  let sanitized = input
    .replace(/<script[^>]*>.*?<\/script>/gi, '')
    .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
    .replace(/javascript\s*:/gi, '')
    .replace(/data\s*:\s*text\/html/gi, '')
    .replace(/expression\s*\(/gi, '')
    .replace(/@import\s+/gi, '')
    .replace(/vbscript\s*:/gi, '')
    .replace(/livescript\s*:/gi, '')
    .replace(/mocha\s*:/gi, '');

  return sanitized.trim();
}

/**
 * Sanitiza string removendo path traversal perigoso
 */
function sanitizePathTraversal(input: string): string {
  if (typeof input !== 'string') return input;

  return input
    .replace(/\.\.\//g, '')
    .replace(/\.\.\\/g, '')
    .replace(/\.\.%2f/gi, '')
    .replace(/\.\.%5c/gi, '')
    .replace(/%2e%2e%2f/gi, '')
    .replace(/%2e%2e%5c/gi, '')
    .replace(/\.\.\x2f/g, '')
    .replace(/\.\.\x5c/g, '')
    .trim();
}
function sanitizeHtml(input: string): string {
  if (typeof input !== 'string') return input;

  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br', 'ul', 'ol', 'li'],
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true,
    RETURN_DOM: false,
  });
}

/**
 * Detecta tentativas de SQL Injection
 */
function detectSqlInjection(value: string): boolean {
  if (typeof value !== 'string') return false;

  return SQL_INJECTION_PATTERNS.some((pattern) => pattern.test(value));
}

/**
 * Detecta tentativas de XSS
 */
function detectXss(value: string): boolean {
  if (typeof value !== 'string') return false;

  return XSS_PATTERNS.some((pattern) => pattern.test(value));
}

/**
 * Detecta tentativas de Path Traversal
 */
function detectPathTraversal(value: string): boolean {
  if (typeof value !== 'string') return false;

  return PATH_TRAVERSAL_PATTERNS.some((pattern) => pattern.test(value));
}

/**
 * Sanitiza recursivamente um objeto
 */
function sanitizeObject(
  obj: unknown,
  options: SecurityMiddlewareOptions
): { sanitized: unknown; threats: string[] } {
  const threats: string[] = [];

  function sanitizeValue(value: unknown, key?: string): unknown {
    if (value === null || value === undefined) {
      return value;
    }

    if (typeof value === 'string') {
      if (options.enableSqlInjectionProtection && detectSqlInjection(value)) {
        threats.push(
          `SQL Injection attempt detected in field: ${key || 'unknown'}`
        );
        if (options.strictMode) {
          return '';
        }
      }

      if (options.enableXssProtection && detectXss(value)) {
        threats.push(`XSS attempt detected in field: ${key || 'unknown'}`);
        if (options.strictMode) {
          return '';
        }
      }

      if (options.enablePathTraversalProtection && detectPathTraversal(value)) {
        threats.push(
          `Path Traversal attempt detected in field: ${key || 'unknown'}`
        );
        if (options.strictMode) {
          return '';
        }
      }

      if (options.enableInputSanitization) {
        let sanitized = value;

        if (options.enableSqlInjectionProtection) {
          sanitized = sanitizeSqlInput(sanitized);
        }

        if (options.enableXssProtection) {
          if (key && HTML_FIELDS.includes(key.toLowerCase())) {
            sanitized = sanitizeHtml(sanitized);
          } else {
            sanitized = sanitizeXssInput(sanitized);
          }
        }

        if (options.enablePathTraversalProtection) {
          sanitized = sanitizePathTraversal(sanitized);
        }

        return sanitized;
      }

      return value;
    }

    if (Array.isArray(value)) {
      return value.map((item, index) =>
        sanitizeValue(item, `${key}[${index}]`)
      );
    }

    if (typeof value === 'object') {
      const sanitizedObj: Record<string, unknown> = {};
      for (const [objKey, objValue] of Object.entries(value)) {
        const newKey = key ? `${key}.${objKey}` : objKey;
        sanitizedObj[objKey] = sanitizeValue(objValue, newKey);
      }
      return sanitizedObj;
    }

    return value;
  }

  const sanitized = sanitizeValue(obj);
  return { sanitized, threats };
}

/**
 * Middleware principal de segurança
 */
export function securityMiddleware(options: SecurityMiddlewareOptions = {}) {
  const defaultOptions: SecurityMiddlewareOptions = {
    enableSqlInjectionProtection: true,
    enableXssProtection: true,
    enablePathTraversalProtection: true,
    enableInputSanitization: true,
    strictMode: false,
    logAttempts: true,
    customPatterns: [],
  };

  const config = { ...defaultOptions, ...options };

  return async (request: FastifyRequest, reply: FastifyReply) => {
    const startTime = Date.now();
    const threats: string[] = [];

    try {
      if (request.query && typeof request.query === 'object') {
        const { sanitized, threats: queryThreats } = sanitizeObject(
          request.query,
          config
        );
        request.query = sanitized;
        threats.push(...queryThreats);
      }

      if (request.params && typeof request.params === 'object') {
        const { sanitized, threats: paramThreats } = sanitizeObject(
          request.params,
          config
        );
        request.params = sanitized;
        threats.push(...paramThreats);
      }

      if (request.body && typeof request.body === 'object') {
        const { sanitized, threats: bodyThreats } = sanitizeObject(
          request.body,
          config
        );
        request.body = sanitized;
        threats.push(...bodyThreats);
      }

      const userAgent = request.headers['user-agent'] || '';
      if (detectXss(userAgent) || detectSqlInjection(userAgent)) {
        threats.push('Malicious User-Agent detected');
      }

      const referer = request.headers.referer || '';
      if (referer && (detectXss(referer) || detectSqlInjection(referer))) {
        threats.push('Malicious Referer detected');
      }

      if (threats.length > 0 && config.logAttempts) {
        const clientIp = request.ip || 'unknown';
        const processingTime = Date.now() - startTime;

        const logData = {
          event: 'security_threat_detected',
          ip: clientIp,
          userAgent,
          method: request.method,
          url: request.url,
          threats,
          processingTime,
          timestamp: new Date().toISOString(),
        };

        request.log.warn(logData, 'Security threat detected and mitigated');

        logger.info('Security threat detected and mitigated', logData);
      }

      if (config.strictMode && threats.length > 0) {
        return reply.status(HttpStatus.BAD_REQUEST).send({
          success: false,
          message: 'Malicious input detected',
          code: 'SECURITY_VIOLATION',
          details: config.logAttempts ? threats : undefined,
        });
      }

      reply.headers({
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Content-Security-Policy':
          "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
      });
    } catch (error) {
      const errorData = {
        event: 'security_middleware_error',
        error: error instanceof Error ? error.message : 'Unknown error',
        url: request.url,
        method: request.method,
        ip: request.ip,
      };

      request.log.error(errorData, 'Error in security middleware');

      logger.info('Error in security middleware', errorData);
    }
  };
}
