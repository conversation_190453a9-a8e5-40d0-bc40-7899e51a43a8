import { drizzle } from 'drizzle-orm/node-postgres';
import { migrate } from 'drizzle-orm/node-postgres/migrator';
import { Pool } from 'pg';
import { env } from '../config/env';
import { seedUsers, seedRandomUsers, seedDevelopmentData } from './seeder';

const pool = new Pool({
  connectionString: `postgresql://${env.DB_USER}:${env.DB_PASSWORD}@${env.DB_HOST}:${env.DB_PORT}/${env.DB_NAME}`,
});

const db = drizzle(pool);

// Função para executar migrações
async function runMigrations() {
  console.log('🚀 Running migrations...');
  
  try {
    await migrate(db, { migrationsFolder: './drizzle' });
    console.log('✅ Migrations completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

// Função para executar seeder
async function runSeeder() {
  console.log('🌱 Running seeder...');
  
  try {
    await seedUsers();
    await seedRandomUsers();
    await seedDevelopmentData();
    console.log('✅ Seeder completed successfully!');
  } catch (error) {
    console.error('❌ Seeder failed:', error);
    throw error;
  }
}

// Função principal de setup
async function setupDatabase() {
  console.log('🔧 Setting up database...');
  
  try {
    // Primeiro executa as migrações
    await runMigrations();
    
    // Depois executa o seeder
    await runSeeder();
    
    console.log('✅ Database setup completed successfully!');
  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Executar apenas se o arquivo for chamado diretamente
if (require.main === module) {
  setupDatabase();
}

export { setupDatabase, runMigrations, runSeeder }; 